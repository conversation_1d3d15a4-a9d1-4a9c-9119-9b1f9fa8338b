"""
航班查询JSON Schema定义

基于设计文档中的Schema结构，定义完整的JSON Schema规范，
支持航班、机场、飞机等模型的结构化查询条件验证。
"""

import json
from typing import Dict, Any, List, Optional
import jsonschema
from jsonschema import validate, ValidationError


# 支持的查询操作类型
QUERY_OPERATORS = {
    "exact": "精确匹配",
    "contains": "包含匹配", 
    "icontains": "不区分大小写包含匹配",
    "startswith": "开头匹配",
    "endswith": "结尾匹配",
    "range": "范围查询",
    "gt": "大于",
    "gte": "大于等于", 
    "lt": "小于",
    "lte": "小于等于",
    "isnull": "空值检查",
    "array_contains": "数组包含",
    "in": "在列表中"
}

# 支持的聚合函数类型
AGGREGATE_FUNCTIONS = {
    "count": "计数",
    "sum": "求和",
    "avg": "平均值",
    "min": "最小值",
    "max": "最大值",
    "stddev": "标准差",
    "variance": "方差"
}

# 支持的注解函数类型
ANNOTATION_FUNCTIONS = {
    "count": "计数注解",
    "sum": "求和注解",
    "avg": "平均值注解",
    "min": "最小值注解",
    "max": "最大值注解",
    "rank": "排名注解",
    "dense_rank": "密集排名注解",
    "time_diff": "时间差计算注解",
    "row_number": "行号注解",
    "case_when": "条件注解",
    "concat": "字符串连接注解",
    "extract": "日期提取注解"
}

# 支持的数据模型列表（不再硬编码字段，使用introspect_models动态获取）
SUPPORTED_MODELS = ["aviation_flight", "aviation_airports", "aviation_aircraft"]

# 完整的JSON Schema定义
FLIGHT_QUERY_SCHEMA = {
    "$schema": "http://json-schema.org/draft-07/schema#",
    "title": "航班查询条件Schema",
    "description": "用于验证结构化航班查询条件的JSON Schema",
    "type": "object",
    "properties": {
        "model": {
            "type": "string",
            "enum": SUPPORTED_MODELS,
            "description": "要查询的数据模型名称"
        },
        "conditions": {
            "type": "object",
            "description": "查询条件对象",
            "properties": {
                "AND": {
                    "type": "array",
                    "description": "AND逻辑组合的条件列表",
                    "items": {
                        "oneOf": [
                            {"$ref": "#/definitions/condition"},
                            {"$ref": "#/definitions/logical_condition"}
                        ]
                    }
                },
                "OR": {
                    "type": "array", 
                    "description": "OR逻辑组合的条件列表",
                    "items": {
                        "oneOf": [
                            {"$ref": "#/definitions/condition"},
                            {"$ref": "#/definitions/logical_condition"}
                        ]
                    }
                }
            },
            "additionalProperties": {
                "$ref": "#/definitions/condition"
            }
        },
        "ordering": {
            "type": "array",
            "description": "排序字段列表，支持'-'前缀表示降序",
            "items": {"type": "string"},
            "examples": [["departure_scheduled_time"], ["-arrival_actual_time"]]
        },
        "limit": {
            "type": "integer",
            "minimum": 1,
            "maximum": 1000,
            "default": 100,
            "description": "返回结果数量限制"
        },
        "offset": {
            "type": "integer",
            "minimum": 0,
            "default": 0,
            "description": "结果偏移量，用于分页"
        },
        "aggregations": {
            "type": "object",
            "description": "聚合查询配置",
            "properties": {
                "group_by": {
                    "type": "array",
                    "description": "分组字段列表",
                    "items": {"type": "string"}
                },
                "functions": {
                    "type": "object",
                    "description": "聚合函数配置",
                    "additionalProperties": {
                        "$ref": "#/definitions/aggregate_function"
                    }
                }
            },
            "additionalProperties": False
        },
        "annotations": {
            "type": "object",
            "description": "注解字段配置",
            "additionalProperties": {
                "$ref": "#/definitions/annotation_function"
            }
        },
        "conditions_after_annotations": {
            "type": "object",
            "description": "在注解计算后应用的查询条件，用于过滤注解字段",
            "properties": {
                "AND": {
                    "type": "array",
                    "description": "AND逻辑组合的条件列表",
                    "items": {
                        "oneOf": [
                            {"$ref": "#/definitions/condition"},
                            {"$ref": "#/definitions/logical_condition"}
                        ]
                    }
                },
                "OR": {
                    "type": "array", 
                    "description": "OR逻辑组合的条件列表",
                    "items": {
                        "oneOf": [
                            {"$ref": "#/definitions/condition"},
                            {"$ref": "#/definitions/logical_condition"}
                        ]
                    }
                }
            },
            "additionalProperties": {
                "$ref": "#/definitions/condition"
            }
        }
    },
    "required": ["model"],
    "additionalProperties": False,
    
    "definitions": {
        "condition": {
            "type": "object",
            "properties": {
                "field": {
                    "type": "string",
                    "description": "查询字段名，支持关联查询如'aircraft__model_name'"
                },
                "operator": {
                    "type": "string",
                    "enum": list(QUERY_OPERATORS.keys()),
                    "description": "查询操作符"
                },
                "value": {
                    "description": "查询值，类型根据操作符而定",
                    "oneOf": [
                        {"type": "string"},
                        {"type": "number"},
                        {"type": "boolean"},
                        {"type": "null"},
                        {
                            "type": "array",
                            "description": "用于range、in、array_contains等操作"
                        }
                    ]
                }
            },
            "required": ["field", "operator", "value"],
            "additionalProperties": False
        },
        "logical_condition": {
            "type": "object",
            "properties": {
                "AND": {
                    "type": "array",
                    "items": {"$ref": "#/definitions/condition"}
                },
                "OR": {
                    "type": "array",
                    "items": {"$ref": "#/definitions/condition"}
                }
            },
            "additionalProperties": False
        },
        "aggregate_function": {
            "type": "object",
            "properties": {
                "function": {
                    "type": "string",
                    "enum": list(AGGREGATE_FUNCTIONS.keys()),
                    "description": "聚合函数名称"
                },
                "field": {
                    "type": "string",
                    "description": "聚合字段名，count函数可以为空"
                },
                "distinct": {
                    "type": "boolean",
                    "default": False,
                    "description": "是否去重"
                },
                "filter": {
                    "$ref": "#/definitions/condition",
                    "description": "聚合过滤条件"
                }
            },
            "required": ["function"],
            "additionalProperties": False
        },
        "annotation_function": {
            "type": "object",
            "properties": {
                "function": {
                    "type": "string",
                    "enum": list(ANNOTATION_FUNCTIONS.keys()),
                    "description": "注解函数名称"
                },
                "field": {
                    "type": "string",
                    "description": "注解字段名"
                },
                "parameters": {
                    "type": "object",
                    "description": "函数参数",
                    "additionalProperties": True
                },
                "order_by": {
                    "type": "array",
                    "description": "排序字段（用于窗口函数）",
                    "items": {"type": "string"}
                },
                "partition_by": {
                    "type": "array",
                    "description": "分区字段（用于窗口函数）",
                    "items": {"type": "string"}
                }
            },
            "required": ["function"],
            "additionalProperties": True,
            "description": "注解函数配置，支持灵活的函数参数。除了基础属性外，每个函数可以定义自己特定的参数，如：time_diff函数的field1、field2、unit等"
        }
    }
}


def get_supported_models() -> List[str]:
    """获取支持的数据模型列表"""
    return SUPPORTED_MODELS


def get_supported_operators() -> Dict[str, str]:
    """获取支持的查询操作符"""
    return QUERY_OPERATORS


def get_supported_aggregate_functions() -> Dict[str, str]:
    """获取支持的聚合函数"""
    return AGGREGATE_FUNCTIONS


def get_supported_annotation_functions() -> Dict[str, str]:
    """获取支持的注解函数"""
    return ANNOTATION_FUNCTIONS