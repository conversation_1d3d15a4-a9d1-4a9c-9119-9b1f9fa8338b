"""
查询解析器

解析JSON查询条件并转换为Django ORM查询，支持各种查询操作类型
和复杂的逻辑组合（AND, OR）以及关联查询构建。
"""

import json
from typing import Dict, Any, List, Optional, Union
from django.db.models import Q, QuerySet, Count, Sum, Av<PERSON>, <PERSON>, Max, StdDev, Variance
from django.db.models import Case, When, Value, F, Window
from django.db.models.functions import Rank, DenseRank, RowNumber, Concat, Extract
from django.apps import apps

from .flight_query_schema import (
    get_supported_models, 
    get_supported_operators,
    get_supported_aggregate_functions,
    get_supported_annotation_functions
)


class QueryParseError(Exception):
    """查询解析异常"""
    pass


class QueryParser:
    """
    查询解析器类
    
    负责将JSON格式的查询条件解析为Django ORM QuerySet，
    支持复杂的查询条件组合和关联查询。
    """
    
    def __init__(self):
        self.supported_models = get_supported_models()
        self.supported_operators = get_supported_operators()
        self.supported_aggregate_functions = get_supported_aggregate_functions()
        self.supported_annotation_functions = get_supported_annotation_functions()
        self._model_cache = {}
    
    def parse_json_query(self, json_query: Union[str, Dict[str, Any]]) -> QuerySet:
        """
        解析JSON查询条件为Django QuerySet
        
        Args:
            json_query: JSON字符串或字典格式的查询条件
            
        Returns:
            QuerySet: Django ORM查询集
            
        Raises:
            QueryParseError: 查询解析失败时抛出
        """
        try:
            # 处理输入格式
            if isinstance(json_query, str):
                query_data = json.loads(json_query)
            else:
                query_data = json_query
            
            # 自动修复常见的结构错误
            query_data = self._fix_query_structure(query_data)
            
            # 假设输入已经过验证（由上层验证器处理）
            # 获取模型
            model_name = query_data["model"]
            model_class = self._get_model_class(model_name)
            
            # 构建基础QuerySet
            queryset = model_class.objects.all()
            
            # 解析查询条件
            conditions = query_data.get("conditions", {})
            if conditions:
                filter_q = self._build_filter_conditions(conditions)
                queryset = queryset.filter(filter_q)
            
            # 处理注解
            annotations = query_data.get("annotations", {})
            if annotations:
                annotation_dict = self._build_annotations(annotations)
                queryset = queryset.annotate(**annotation_dict)
            
            # 处理聚合
            aggregations = query_data.get("aggregations", {})
            if aggregations:
                return self._build_aggregations(queryset, aggregations)
            
            # 应用排序
            ordering = query_data.get("ordering", [])
            if ordering:
                queryset = queryset.order_by(*ordering)
            
            # 应用分页
            offset = query_data.get("offset", 0)
            limit = query_data.get("limit", 100)
            
            if offset > 0:
                queryset = queryset[offset:offset + limit]
            else:
                queryset = queryset[:limit]
            
            return queryset
            
        except json.JSONDecodeError as e:
            raise QueryParseError(f"JSON解析失败: {str(e)}")
        except Exception as e:
            raise QueryParseError(f"查询解析失败: {str(e)}")
    
    def _get_model_class(self, model_name: str):
        """获取Django模型类"""
        if model_name in self._model_cache:
            return self._model_cache[model_name]
        
        try:
            # 从flights应用获取模型
            model_class = apps.get_model('flights', model_name)
            self._model_cache[model_name] = model_class
            return model_class
        except LookupError:
            raise QueryParseError(f"模型 '{model_name}' 不存在")
    
    def _build_filter_conditions(self, conditions: Dict[str, Any]) -> Q:
        """
        构建Django Q对象过滤条件
        
        Args:
            conditions: 查询条件字典
            
        Returns:
            Q: Django Q对象
        """
        if not conditions:
            return Q()
        
        # 处理逻辑组合
        if "AND" in conditions:
            and_conditions = conditions["AND"]
            q_objects = []
            for cond in and_conditions:
                if isinstance(cond, dict):
                    if "field" in cond:
                        # 标准单条件
                        q_objects.append(self._build_single_condition(cond))
                    elif "AND" in cond or "OR" in cond:
                        # 嵌套逻辑条件
                        q_objects.append(self._build_filter_conditions(cond))
                    else:
                        # 其他格式的条件
                        q_objects.append(self._build_filter_conditions(cond))
            
            result_q = Q()
            for q_obj in q_objects:
                result_q &= q_obj
            
            # 处理其他非逻辑组合条件
            other_conditions = {k: v for k, v in conditions.items() if k != "AND"}
            if other_conditions:
                other_q = self._build_filter_conditions(other_conditions)
                result_q &= other_q
            
            return result_q
        
        elif "OR" in conditions:
            or_conditions = conditions["OR"]
            q_objects = []
            for cond in or_conditions:
                if isinstance(cond, dict):
                    if "field" in cond:
                        # 标准单条件
                        q_objects.append(self._build_single_condition(cond))
                    elif "AND" in cond or "OR" in cond:
                        # 嵌套逻辑条件
                        q_objects.append(self._build_filter_conditions(cond))
                    else:
                        # 其他格式的条件
                        q_objects.append(self._build_filter_conditions(cond))
            
            result_q = Q()
            for q_obj in q_objects:
                result_q |= q_obj
            
            # 处理其他非逻辑组合条件
            other_conditions = {k: v for k, v in conditions.items() if k != "OR"}
            if other_conditions:
                other_q = self._build_filter_conditions(other_conditions)
                result_q &= other_q
            
            return result_q
        
        else:
            # 处理直接条件
            q_objects = []
            for key, condition in conditions.items():
                if isinstance(condition, dict) and "field" in condition:
                    # 标准条件格式
                    q_objects.append(self._build_single_condition(condition))
                else:
                    # 简化条件格式 (向后兼容)
                    simple_condition = {"field": key, "operator": "exact", "value": condition}
                    q_objects.append(self._build_single_condition(simple_condition))
            
            # 默认使用AND组合多个条件
            result_q = Q()
            for q_obj in q_objects:
                result_q &= q_obj
            
            return result_q
    
    def _build_single_condition(self, condition: Dict[str, Any]) -> Q:
        """
        构建单个查询条件的Q对象
        
        Args:
            condition: 单个条件字典，包含field、operator、value
            
        Returns:
            Q: Django Q对象
        """
        field = condition["field"]
        operator = condition["operator"]
        value = condition["value"]
        
        # 验证操作符
        if operator not in self.supported_operators:
            raise QueryParseError(f"不支持的操作符: {operator}")
        
        # 根据操作符构建查询
        try:
            return self._apply_operator(field, operator, value)
        except Exception as e:
            raise QueryParseError(f"构建查询条件失败 - 字段: {field}, 操作符: {operator}, 值: {value}, 错误: {str(e)}")
    
    def _apply_operator(self, field: str, operator: str, value: Any) -> Q:
        """
        根据操作符应用查询条件
        
        Args:
            field: 字段名
            operator: 操作符
            value: 查询值
            
        Returns:
            Q: Django Q对象
        """
        # 处理不同的操作符
        if operator == "exact":
            return Q(**{field: value})
        
        elif operator == "contains":
            return Q(**{f"{field}__contains": value})
        
        elif operator == "icontains":
            return Q(**{f"{field}__icontains": value})
        
        elif operator == "startswith":
            return Q(**{f"{field}__startswith": value})
        
        elif operator == "endswith":
            return Q(**{f"{field}__endswith": value})
        
        elif operator == "range":
            if not isinstance(value, list) or len(value) != 2:
                raise QueryParseError("range操作符需要包含两个元素的数组")
            return Q(**{f"{field}__range": value})
        
        elif operator == "gt":
            return Q(**{f"{field}__gt": value})
        
        elif operator == "gte":
            return Q(**{f"{field}__gte": value})
        
        elif operator == "lt":
            return Q(**{f"{field}__lt": value})
        
        elif operator == "lte":
            return Q(**{f"{field}__lte": value})
        
        elif operator == "isnull":
            return Q(**{f"{field}__isnull": value})
        
        elif operator == "in":
            if not isinstance(value, list):
                raise QueryParseError("in操作符需要数组类型的值")
            return Q(**{f"{field}__in": value})
        
        elif operator == "array_contains":
            # PostgreSQL数组包含查询
            return Q(**{f"{field}__contains": [value]})
        
        else:
            raise QueryParseError(f"未实现的操作符: {operator}")
    
    def _build_annotations(self, annotations: Dict[str, Any]) -> Dict[str, Any]:
        """
        构建注解字典
        
        Args:
            annotations: 注解配置字典
            
        Returns:
            Dict: Django注解字典
        """
        annotation_dict = {}
        
        for ann_name, ann_config in annotations.items():
            function_type = ann_config["function"]
            field = ann_config.get("field")
            parameters = ann_config.get("parameters", {})
            order_by = ann_config.get("order_by", [])
            partition_by = ann_config.get("partition_by", [])
            
            try:
                annotation_expr = self._build_annotation_expression(
                    function_type, field, parameters, order_by, partition_by
                )
                annotation_dict[ann_name] = annotation_expr
            except Exception as e:
                raise QueryParseError(f"构建注解 '{ann_name}' 失败: {str(e)}")
        
        return annotation_dict
    
    def _build_annotation_expression(self, function_type: str, field: Optional[str], 
                                   parameters: Dict[str, Any], order_by: List[str], 
                                   partition_by: List[str]):
        """构建单个注解表达式"""
        
        if function_type == "count":
            if field:
                return Count(field, distinct=parameters.get("distinct", False))
            else:
                return Count('*')
        
        elif function_type == "sum":
            if not field:
                raise QueryParseError("sum注解需要指定字段")
            return Sum(field)
        
        elif function_type == "avg":
            if not field:
                raise QueryParseError("avg注解需要指定字段")
            return Avg(field)
        
        elif function_type == "min":
            if not field:
                raise QueryParseError("min注解需要指定字段")
            return Min(field)
        
        elif function_type == "max":
            if not field:
                raise QueryParseError("max注解需要指定字段")
            return Max(field)
        
        elif function_type == "rank":
            if not order_by:
                raise QueryParseError("rank注解需要指定排序字段")
            window_expr = Window(expression=Rank(), order_by=order_by)
            if partition_by:
                window_expr = Window(expression=Rank(), order_by=order_by, partition_by=partition_by)
            return window_expr
        
        elif function_type == "dense_rank":
            if not order_by:
                raise QueryParseError("dense_rank注解需要指定排序字段")
            window_expr = Window(expression=DenseRank(), order_by=order_by)
            if partition_by:
                window_expr = Window(expression=DenseRank(), order_by=order_by, partition_by=partition_by)
            return window_expr
        
        elif function_type == "row_number":
            if not order_by:
                raise QueryParseError("row_number注解需要指定排序字段")
            window_expr = Window(expression=RowNumber(), order_by=order_by)
            if partition_by:
                window_expr = Window(expression=RowNumber(), order_by=order_by, partition_by=partition_by)
            return window_expr
        
        elif function_type == "case_when":
            cases = parameters.get("cases", [])
            default = parameters.get("default")
            
            when_conditions = []
            for case in cases:
                condition = case.get("condition", {})
                value = case.get("value")
                if condition and value is not None:
                    q_condition = self._build_single_condition(condition)
                    when_conditions.append(When(q_condition, then=Value(value)))
            
            if when_conditions:
                case_expr = Case(*when_conditions)
                if default is not None:
                    case_expr = Case(*when_conditions, default=Value(default))
                return case_expr
            else:
                raise QueryParseError("case_when注解需要至少一个条件")
        
        elif function_type == "concat":
            fields = parameters.get("fields", [])
            separator = parameters.get("separator", "")
            
            if not fields:
                raise QueryParseError("concat注解需要指定字段列表")
            
            if len(fields) == 1:
                return F(fields[0])
            elif len(fields) == 2:
                return Concat(F(fields[0]), Value(separator), F(fields[1]))
            else:
                # 多个字段连接
                concat_args = []
                for i, field in enumerate(fields):
                    concat_args.append(F(field))
                    if i < len(fields) - 1:
                        concat_args.append(Value(separator))
                return Concat(*concat_args)
        
        elif function_type == "extract":
            if not field:
                raise QueryParseError("extract注解需要指定字段")
            lookup_type = parameters.get("lookup_type", "year")
            return Extract(field, lookup_type)
        
        else:
            raise QueryParseError(f"不支持的注解函数: {function_type}")
    
    def _build_aggregations(self, queryset: QuerySet, aggregations: Dict[str, Any]) -> Dict[str, Any]:
        """
        构建聚合查询
        
        Args:
            queryset: 基础查询集
            aggregations: 聚合配置
            
        Returns:
            Dict: 聚合结果
        """
        try:
            group_by = aggregations.get("group_by", [])
            functions = aggregations.get("functions", {})
            
            # 构建聚合函数字典
            aggregate_dict = {}
            for func_name, func_config in functions.items():
                function_type = func_config["function"]
                field = func_config.get("field")
                distinct = func_config.get("distinct", False)
                filter_condition = func_config.get("filter")
                
                try:
                    agg_expr = self._build_aggregate_expression(function_type, field, distinct, filter_condition)
                    aggregate_dict[func_name] = agg_expr
                except Exception as e:
                    raise QueryParseError(f"构建聚合函数 '{func_name}' 失败: {str(e)}")
            
            # 执行聚合查询
            if group_by:
                # 分组聚合 - 正确的方法是先values再annotate
                try:
                    # 使用values()进行分组，然后使用annotate()进行聚合
                    result_queryset = queryset.values(*group_by).annotate(**aggregate_dict).order_by(*group_by)
                    # 转换为列表以避免延迟执行问题
                    return list(result_queryset)
                except Exception as e:
                    raise QueryParseError(f"分组聚合查询失败: {str(e)}")
            else:
                # 全局聚合
                try:
                    return queryset.aggregate(**aggregate_dict)
                except Exception as e:
                    raise QueryParseError(f"全局聚合查询失败: {str(e)}")
                    
        except Exception as e:
            raise QueryParseError(f"聚合查询构建失败: {str(e)}")
    
    def _build_aggregate_expression(self, function_type: str, field: Optional[str], 
                                  distinct: bool = False, filter_condition: Optional[Dict[str, Any]] = None):
        """构建单个聚合表达式"""
        
        try:
            # 构建过滤条件
            filter_q = None
            if filter_condition:
                filter_q = self._build_single_condition(filter_condition)
            
            # 构建聚合表达式
            agg_expr = None
            
            if function_type == "count":
                if field and field != '*':
                    agg_expr = Count(field, distinct=distinct)
                else:
                    # 对于count(*)，使用主键或任意非空字段
                    agg_expr = Count('*')
            
            elif function_type == "sum":
                if not field:
                    raise QueryParseError("sum聚合需要指定字段")
                agg_expr = Sum(field)
                if distinct:
                    # Sum不直接支持distinct，需要特殊处理
                    agg_expr = Sum(field, distinct=distinct)
            
            elif function_type == "avg":
                if not field:
                    raise QueryParseError("avg聚合需要指定字段")
                agg_expr = Avg(field)
                if distinct:
                    agg_expr = Avg(field, distinct=distinct)
            
            elif function_type == "min":
                if not field:
                    raise QueryParseError("min聚合需要指定字段")
                agg_expr = Min(field)
            
            elif function_type == "max":
                if not field:
                    raise QueryParseError("max聚合需要指定字段")
                agg_expr = Max(field)
            
            elif function_type == "stddev":
                if not field:
                    raise QueryParseError("stddev聚合需要指定字段")
                agg_expr = StdDev(field)
            
            elif function_type == "variance":
                if not field:
                    raise QueryParseError("variance聚合需要指定字段")
                agg_expr = Variance(field)
            
            else:
                raise QueryParseError(f"不支持的聚合函数: {function_type}")
            
            # 应用过滤条件（如果支持）
            if filter_q and hasattr(agg_expr, 'filter'):
                try:
                    agg_expr = agg_expr.filter(filter_q)
                except Exception:
                    # 如果filter方法不可用，忽略过滤条件
                    pass
            
            return agg_expr
            
        except Exception as e:
            raise QueryParseError(f"构建聚合表达式失败 - 函数: {function_type}, 字段: {field}, 错误: {str(e)}")
    
    def validate_query_conditions(self, conditions: Dict[str, Any]) -> tuple[bool, Optional[str]]:
        """
        轻量级查询条件验证 - 主要用于ORM构建前的基础检查
        
        Args:
            conditions: 查询条件字典
            
        Returns:
            tuple: (是否有效, 错误信息)
        """
        try:
            # 简单检查：尝试构建Q对象来验证条件的ORM兼容性
            self._build_filter_conditions(conditions)
            return True, None
        except Exception as e:
            return False, f"ORM构建失败: {str(e)}"
    
    def _count_conditions(self, conditions: Dict[str, Any]) -> int:
        """递归计算条件数量"""
        count = 0
        for key, value in conditions.items():
            if key in ["AND", "OR"]:
                if isinstance(value, list):
                    for cond in value:
                        count += self._count_conditions(cond) if isinstance(cond, dict) else 1
            else:
                count += 1
        return count
    
    def _assess_query_complexity(self, conditions: Dict[str, Any]) -> str:
        """评估查询复杂度"""
        condition_count = self._count_conditions(conditions)
        has_logic_ops = "AND" in conditions or "OR" in conditions
        
        if condition_count == 0:
            return "无条件"
        elif condition_count == 1 and not has_logic_ops:
            return "简单"
        elif condition_count <= 3 and not has_logic_ops:
            return "中等"
        elif has_logic_ops or condition_count > 3:
            return "复杂"
        else:
            return "未知"
    
    def _fix_query_structure(self, query_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        修复常见的查询结构错误
        
        Args:
            query_data: 原始查询数据
            
        Returns:
            Dict: 修复后的查询数据
        """
        fixed_data = query_data.copy()
        
        # 修复 group_by 位置错误
        if "group_by" in fixed_data and "aggregations" in fixed_data:
            # 将根级别的 group_by 移动到 aggregations 内部
            group_by = fixed_data.pop("group_by")
            if "group_by" not in fixed_data["aggregations"]:
                fixed_data["aggregations"]["group_by"] = group_by
        
        # 确保 aggregations 结构正确
        if "aggregations" in fixed_data:
            agg = fixed_data["aggregations"]
            
            # 如果 aggregations 直接包含函数定义，需要包装到 functions 中
            if any(key not in ["group_by", "functions"] for key in agg.keys()):
                functions = {}
                group_by = agg.get("group_by", [])
                
                for key, value in agg.items():
                    if key != "group_by":
                        functions[key] = value
                
                fixed_data["aggregations"] = {
                    "functions": functions
                }
                
                if group_by:
                    fixed_data["aggregations"]["group_by"] = group_by
        
        return fixed_data


# 全局查询解析器实例
query_parser = QueryParser()


def parse_flight_query(json_query: Union[str, Dict[str, Any]]) -> QuerySet:
    """
    解析航班查询的便捷函数
    
    Args:
        json_query: JSON查询条件
        
    Returns:
        QuerySet: Django查询集
    """
    return query_parser.parse_json_query(json_query)
